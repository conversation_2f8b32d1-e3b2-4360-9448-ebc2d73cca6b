<?php

namespace App\Filament\Resources\JadwalKerjaResource\Pages;

use App\Filament\Resources\JadwalKerjaResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ListJadwalKerjas extends ListRecords
{
    protected static string $resource = JadwalKerjaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Buat Jadwal Baru'),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            JadwalKerjaResource\Widgets\JadwalKerjaOverview::class,
        ];
    }

    public function getTabs(): array
    {
        return [
            'semua' => Tab::make('Semua Jadwal'),

            'hari_ini' => Tab::make('Hari Ini')
                ->modifyQueryUsing(fn(Builder $query) => $query->whereDate('tanggal_jadwal', Carbon::today())),

            'minggu_ini' => Tab::make('Minggu Ini')
                ->modifyQueryUsing(fn(Builder $query) => $query->whereBetween('tanggal_jadwal', [
                    Carbon::now()->startOfWeek(),
                    Carbon::now()->endOfWeek()
                ])),

            'bulan_ini' => Tab::make('Bulan Ini')
                ->modifyQueryUsing(fn(Builder $query) => $query->whereBetween('tanggal_jadwal', [
                    Carbon::now()->startOfMonth(),
                    Carbon::now()->endOfMonth()
                ])),

            'belum_disetujui' => Tab::make('Belum Disetujui')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('is_approved', false)),
        ];
    }
}
