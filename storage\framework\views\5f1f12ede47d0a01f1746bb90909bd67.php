<div class="mention-textarea-component space-y-2">
    <!--[if BLOCK]><![endif]--><?php if($label): ?>
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            <?php echo e($label); ?>

            <!--[if BLOCK]><![endif]--><?php if($required): ?>
                <span class="text-red-500">*</span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </label>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <div class="relative">
        <textarea
            wire:model.live="value"
            x-data="{
                handleKeyup(event) {
                    const value = event.target.value;
                    const cursorPos = event.target.selectionStart;
                    const textBeforeCursor = value.substring(0, cursorPos);
                    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);

                    if (mentionMatch) {
                        $wire.searchMentions(mentionMatch[1]);
                    } else {
                        $wire.searchMentions('');
                    }
                }
            }"
            x-on:keyup="handleKeyup($event)"
            placeholder="<?php echo e($placeholder); ?>"
            rows="<?php echo e($rows); ?>"
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 resize-none"
            <?php if($required): ?> required <?php endif; ?>
        ></textarea>

        <!-- Mention Suggestions -->
        <!--[if BLOCK]><![endif]--><?php if($showMentions && !empty($mentionSuggestions)): ?>
            <div class="mention-suggestions">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mentionSuggestions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <button
                        type="button"
                        wire:click="insertMention('<?php echo e($user['name']); ?>')"
                        class="mention-suggestion"
                    >
                        <div class="mention-avatar">
                            <?php echo e(substr($user['name'], 0, 1)); ?>

                        </div>
                        <div class="mention-info">
                            <div class="mention-name"><?php echo e($user['name']); ?></div>
                            <div class="mention-email"><?php echo e($user['email']); ?></div>
                        </div>
                    </button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!--[if BLOCK]><![endif]--><?php if($name): ?>
        <input type="hidden" name="<?php echo e($name); ?>" value="<?php echo e($value); ?>">
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\laragon\www\viera\resources\views/livewire/mention-textarea.blade.php ENDPATH**/ ?>