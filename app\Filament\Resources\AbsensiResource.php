<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AbsensiResource\Pages;
use App\Models\Absensi;
use App\Models\Karyawan;
use App\Models\Schedule;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Section;
use Filament\Tables\Actions\BulkAction;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use App\Traits\HasExportActions;
use App\Exports\AbsensiExport;
use App\Models\Shift;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Actions\Action;
// carbon
use Carbon\Carbon;

class AbsensiResource extends Resource
{
    use HasExportActions;

    protected static ?string $model = Absensi::class;

    protected static ?string $navigationIcon = 'heroicon-o-finger-print';
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?string $navigationLabel = 'Data Absensi';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Absensi')
                    ->schema([
                        Select::make('karyawan_id')
                            ->label('Karyawan')
                            ->options(function () {
                                // If user is supervisor, only show their supervised employees
                                $user = Auth::user();
                                $query = Karyawan::query()
                                    ->where('status_aktif', 1)
                                    ->orderBy('nama_lengkap');

                                if ($user->role === 'supervisor') {
                                    $employeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
                                    $query->whereIn('id', $employeeIds);
                                }

                                return $query->pluck('nama_lengkap', 'id');
                            })
                            ->required()
                            ->searchable()
                            ->reactive()
                            ->afterStateUpdated(fn(callable $set) => $set('jadwal_id', null)),

                        DatePicker::make('tanggal_absensi')
                            ->label('Tanggal Absensi')
                            ->required()
                            ->default(now())
                            ->reactive()
                            ->afterStateUpdated(fn(callable $set) => $set('jadwal_id', null)),

                        Select::make('jadwal_id')
                            ->label('Jadwal')
                            ->options(function (callable $get) {
                                $karyawanId = $get('karyawan_id');
                                $tanggal = $get('tanggal_absensi');

                                if (!$karyawanId || !$tanggal) {
                                    return [];
                                }

                                return Schedule::where('karyawan_id', $karyawanId)
                                    ->where('tanggal_jadwal', $tanggal)
                                    ->get()
                                    ->mapWithKeys(function ($jadwal) {
                                        $shift = $jadwal->shift ? " ({$jadwal->shift->nama_shift})" : '';
                                        return [$jadwal->id => "Jadwal {$jadwal->tanggal_jadwal->format('d/m/Y')}{$shift}"];
                                    });
                            })
                            ->searchable()
                            ->placeholder('Pilih jadwal jika ada'),

                        Select::make('status')
                            ->label('Status Kehadiran')
                            ->options([
                                'hadir' => 'Hadir',
                                'terlambat' => 'Terlambat',
                                'izin' => 'Izin',
                                'sakit' => 'Sakit',
                                'cuti' => 'Cuti',
                                'alpha' => 'Alpha (Tanpa Keterangan)',
                            ])
                            ->required()
                            ->default('hadir'),
                    ])->columns(2),

                Section::make('Waktu Absensi')
                    ->schema([
                        TimePicker::make('waktu_masuk')
                            ->label('Waktu Masuk')
                            ->seconds(false),

                        TimePicker::make('waktu_keluar')
                            ->label('Waktu Keluar')
                            ->seconds(false),

                        Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->maxLength(1000)
                            ->columnSpanFull(),
                    ])->columns(2),

                Section::make('Bukti Kehadiran')
                    ->schema([
                        FileUpload::make('foto_masuk')
                            ->label('Foto Masuk')
                            ->image()
                            ->directory('absensi/masuk')
                            ->disk('public'),

                        FileUpload::make('foto_keluar')
                            ->label('Foto Keluar')
                            ->image()
                            ->directory('absensi/keluar')
                            ->disk('public'),

                        TextInput::make('lokasi_masuk')
                            ->label('Lokasi Masuk')
                            ->placeholder('Koordinat GPS atau alamat'),

                        TextInput::make('lokasi_keluar')
                            ->label('Lokasi Keluar')
                            ->placeholder('Koordinat GPS atau alamat'),
                    ])->columns(2),

                Section::make('Informasi Lokasi')
                    ->schema([
                        Forms\Components\Placeholder::make('lokasi_info')
                            ->label('Koordinat Lokasi')
                            ->content(function ($record) {
                                if (!$record) {
                                    return 'Data belum tersedia';
                                }

                                $content = '<div style="background: #f8fafc; padding: 12px; border-radius: 6px; border: 1px solid #e2e8f0;">';

                                if ($record->latitude_masuk && $record->longitude_masuk) {
                                    $content .= '<div style="margin-bottom: 8px;">';
                                    $content .= '<strong style="color: #059669;">📍 Lokasi Masuk:</strong><br>';
                                    $content .= '<span style="font-family: monospace; font-size: 14px;">Lat: ' . $record->latitude_masuk . ', Lng: ' . $record->longitude_masuk . '</span><br>';
                                    $content .= '<a href="https://www.google.com/maps?q=' . $record->latitude_masuk . ',' . $record->longitude_masuk . '" target="_blank" style="color: #2563eb; text-decoration: underline; font-size: 13px;">🗺️ Lihat di Google Maps</a>';
                                    $content .= '</div>';
                                }

                                if ($record->latitude_keluar && $record->longitude_keluar) {
                                    $content .= '<div>';
                                    $content .= '<strong style="color: #dc2626;">📍 Lokasi Keluar:</strong><br>';
                                    $content .= '<span style="font-family: monospace; font-size: 14px;">Lat: ' . $record->latitude_keluar . ', Lng: ' . $record->longitude_keluar . '</span><br>';
                                    $content .= '<a href="https://www.google.com/maps?q=' . $record->latitude_keluar . ',' . $record->longitude_keluar . '" target="_blank" style="color: #2563eb; text-decoration: underline; font-size: 13px;">🗺️ Lihat di Google Maps</a>';
                                    $content .= '</div>';
                                }

                                if (!$record->latitude_masuk && !$record->latitude_keluar) {
                                    $content .= '<span style="color: #6b7280;">Tidak ada data lokasi tersedia</span>';
                                }

                                $content .= '</div>';
                                $content .= '<p style="margin-top: 8px; font-size: 12px; color: #6b7280;">💡 Gunakan tombol "Lihat Lokasi" di tabel untuk melihat peta interaktif</p>';

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => $record && ($record->latitude_masuk || $record->latitude_keluar)),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('karyawan.nama_lengkap')
                    ->label('Nama Karyawan')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('tanggal_absensi')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('shift_info')
                    ->label('Shift & Periode')
                    ->getStateUsing(function ($record) {
                        if (!$record->jadwal || !$record->jadwal->shift) {
                            return 'Tidak ada jadwal';
                        }

                        $shift = $record->jadwal->shift;
                        $shiftName = $shift->nama_shift;

                        if ($shift->isSplitShift()) {
                            $periode = $record->periode == 1 ? 'P1' : 'P2';
                            return "{$shiftName} ({$periode})";
                        }

                        return $shiftName;
                    })
                    ->badge()
                    ->color(function ($record) {
                        if (!$record->jadwal || !$record->jadwal->shift) {
                            return 'gray';
                        }

                        if ($record->jadwal->shift->isSplitShift()) {
                            return $record->periode == 1 ? 'success' : 'warning';
                        }

                        return 'primary';
                    }),

                TextColumn::make('waktu_masuk')
                    ->label('Masuk')
                    ->time('H:i')
                    ->sortable(),

                TextColumn::make('waktu_keluar')
                    ->label('Keluar')
                    ->time('H:i')
                    ->sortable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => \App\Enums\AttendanceStatus::from($state)->badgeColor())
                    ->formatStateUsing(fn(string $state): string => \App\Enums\AttendanceStatus::from($state)->label())
                    ->sortable(),

                // menit keterlambatan
                TextColumn::make('menit_terlambat')
                    ->label('Menit Terlambat')
                    ->getStateUsing(function ($record) {
                        if (!$record->waktu_masuk || !$record->jadwal) {
                            return 0;
                        }

                        $shift = $record->jadwal->shift;
                        if (!$shift) {
                            return 0;
                        }

                        $waktuMasukShift = Carbon::parse($record->tanggal_absensi->format('Y-m-d') . ' ' . $shift->waktu_mulai);
                        $waktuMasukAktual = Carbon::parse($record->waktu_masuk);
                        return $waktuMasukShift->diffInMinutes($waktuMasukAktual);
                    })
                    ->sortable(),

                TextColumn::make('approvedBy.name')
                    ->label('Disetujui Oleh')
                    ->sortable(),

                TextColumn::make('approved_at')
                    ->label('Disetujui Pada')
                    ->dateTime('d M Y H:i')
                    ->sortable(),

                TextColumn::make('foto_masuk')
                    ->label('Foto Masuk')
                    ->formatStateUsing(function ($record) {
                        $photoUrl = $record->foto_masuk ? asset('storage/' . $record->foto_masuk) : null;
                        $metadata = $record->metadata_foto_masuk ?? [];

                        if (!$photoUrl) {
                            return new \Illuminate\Support\HtmlString('<div style="color: #9ca3af; font-size: 12px;">No Photo</div>');
                        }

                        $hasMetadata = !empty($metadata);
                        $indicator = $hasMetadata ? '<div style="position: absolute; top: -4px; right: -4px; width: 12px; height: 12px; background: #10B981; border: 1px solid white; border-radius: 50%;" title="Foto dengan metadata"></div>' : '';

                        return new \Illuminate\Support\HtmlString('
                            <div style="position: relative; display: inline-block;">
                                <img src="' . $photoUrl . '"
                                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 6px; cursor: pointer;"
                                     onclick="showPhotoModal(\'' . $photoUrl . '\', ' . htmlspecialchars(json_encode(\App\Services\PhotoMetadataService::formatMetadataForDisplay($metadata))) . ', \'masuk\')"
                                     title="Klik untuk melihat detail">
                                ' . $indicator . '
                            </div>
                        ');
                    })
                    ->html()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('foto_keluar')
                    ->label('Foto Keluar')
                    ->formatStateUsing(function ($record) {
                        $photoUrl = $record->foto_keluar ? asset('storage/' . $record->foto_keluar) : null;
                        $metadata = $record->metadata_foto_keluar ?? [];

                        if (!$photoUrl) {
                            return new \Illuminate\Support\HtmlString('<div style="color: #9ca3af; font-size: 12px;">No Photo</div>');
                        }

                        $hasMetadata = !empty($metadata);
                        $indicator = $hasMetadata ? '<div style="position: absolute; top: -4px; right: -4px; width: 12px; height: 12px; background: #10B981; border: 1px solid white; border-radius: 50%;" title="Foto dengan metadata"></div>' : '';

                        return new \Illuminate\Support\HtmlString('
                            <div style="position: relative; display: inline-block;">
                                <img src="' . $photoUrl . '"
                                     style="width: 40px; height: 40px; object-fit: cover; border-radius: 6px; cursor: pointer;"
                                     onclick="showPhotoModal(\'' . $photoUrl . '\', ' . htmlspecialchars(json_encode(\App\Services\PhotoMetadataService::formatMetadataForDisplay($metadata))) . ', \'keluar\')"
                                     title="Klik untuk melihat detail">
                                ' . $indicator . '
                            </div>
                        ');
                    })
                    ->html()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('lokasi_info')
                    ->label('Lokasi')
                    ->getStateUsing(function (Absensi $record) {
                        $locations = [];
                        if ($record->latitude_masuk && $record->longitude_masuk) {
                            $locations[] = 'Masuk: ✓';
                        }
                        if ($record->latitude_keluar && $record->longitude_keluar) {
                            $locations[] = 'Keluar: ✓';
                        }
                        return empty($locations) ? 'Tidak ada' : implode(' | ', $locations);
                    })
                    ->badge()
                    ->color(fn(string $state): string => $state === 'Tidak ada' ? 'gray' : 'success')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('geofencing_status')
                    ->label('Geofencing')
                    ->getStateUsing(function (Absensi $record) {
                        if (!$record->karyawan || !$record->karyawan->entitas) {
                            return 'No Entitas';
                        }

                        $entitas = $record->karyawan->entitas;

                        if (!$entitas->enable_geofencing) {
                            return 'Disabled';
                        }

                        if (!$record->latitude_masuk || !$record->longitude_masuk) {
                            return 'No Location';
                        }

                        $distance = $record->distance_from_entitas;

                        if ($distance === null) {
                            return 'Error';
                        }

                        $isValid = $record->is_location_valid;
                        $formattedDistance = \App\Services\GeofencingService::formatDistance($distance);

                        return $isValid
                            ? "Valid ({$formattedDistance})"
                            : "Invalid ({$formattedDistance})";
                    })
                    ->badge()
                    ->color(function (string $state) {
                        if (str_contains($state, 'Valid')) return 'success';
                        if (str_contains($state, 'Invalid')) return 'danger';
                        if ($state === 'Disabled') return 'warning';
                        return 'gray';
                    })
                    ->tooltip(function (Absensi $record) {
                        if (!$record->karyawan || !$record->karyawan->entitas) {
                            return 'Karyawan tidak memiliki entitas';
                        }

                        $entitas = $record->karyawan->entitas;

                        if (!$entitas->enable_geofencing) {
                            return 'Geofencing tidak diaktifkan untuk entitas ini';
                        }

                        if (!$record->latitude_masuk || !$record->longitude_masuk) {
                            return 'Tidak ada data lokasi absensi';
                        }

                        $distance = $record->distance_from_entitas;

                        if ($distance === null) {
                            return 'Error menghitung jarak';
                        }

                        $formattedDistance = \App\Services\GeofencingService::formatDistance($distance);

                        return "Jarak dari {$entitas->nama}: {$formattedDistance} (Max: {$entitas->radius}m)";
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'hadir' => 'Hadir',
                        'terlambat' => 'Terlambat',
                        'izin' => 'Izin',
                        'sakit' => 'Sakit',
                        'cuti' => 'Cuti',
                        'alpha' => 'Alpha',
                    ]),

                SelectFilter::make('periode')
                    ->label('Periode Shift')
                    ->options([
                        '1' => 'Periode 1',
                        '2' => 'Periode 2',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['value']) && $data['value'] !== '') {
                            return $query->where('periode', $data['value']);
                        }
                        return $query;
                    }),

                Filter::make('split_shift')
                    ->label('Hanya Split Shift')
                    ->query(function (Builder $query): Builder {
                        return $query->whereHas('jadwal.shift', function ($q) {
                            $q->where('is_split_shift', true);
                        });
                    })
                    ->toggle(),

                Filter::make('tanggal_absensi')
                    ->form([
                        DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_absensi', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_absensi', '<=', $date),
                            );
                    }),

                Filter::make('approved')
                    ->label('Status Persetujuan')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('approved_at'))
                    ->toggle(),

                Filter::make('not_approved')
                    ->label('Belum Disetujui')
                    ->query(fn(Builder $query): Builder => $query->whereNull('approved_at'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('viewLocation')
                    ->label('Lihat Lokasi')
                    ->icon('heroicon-o-map-pin')
                    ->color('info')
                    ->visible(fn(Absensi $record) => $record->latitude_masuk || $record->latitude_keluar)
                    ->modalHeading('Lokasi Absensi')
                    ->modalContent(function (Absensi $record) {
                        $content = '
                        <div style="display: grid; gap: 20px;">
                            <div style="background: #f8fafc; padding: 16px; border-radius: 8px; border: 1px solid #e2e8f0;">
                                <h4 style="margin: 0 0 12px 0; font-weight: 600; color: #1e293b;">Informasi Lokasi</h4>';

                        if ($record->latitude_masuk && $record->longitude_masuk) {
                            $content .= '
                                <div style="margin-bottom: 16px;">
                                    <strong style="color: #059669;">📍 Lokasi Masuk:</strong><br>
                                    <span style="font-family: monospace; background: #ecfdf5; padding: 4px 8px; border-radius: 4px; font-size: 14px;">
                                        Lat: ' . $record->latitude_masuk . ', Lng: ' . $record->longitude_masuk . '
                                    </span><br>
                                    <a href="https://www.google.com/maps?q=' . $record->latitude_masuk . ',' . $record->longitude_masuk . '"
                                       target="_blank"
                                       style="color: #2563eb; text-decoration: underline; font-size: 14px;">
                                        🗺️ Buka di Google Maps
                                    </a>
                                </div>';
                        }

                        if ($record->latitude_keluar && $record->longitude_keluar) {
                            $content .= '
                                <div style="margin-bottom: 16px;">
                                    <strong style="color: #dc2626;">📍 Lokasi Keluar:</strong><br>
                                    <span style="font-family: monospace; background: #fef2f2; padding: 4px 8px; border-radius: 4px; font-size: 14px;">
                                        Lat: ' . $record->latitude_keluar . ', Lng: ' . $record->longitude_keluar . '
                                    </span><br>
                                    <a href="https://www.google.com/maps?q=' . $record->latitude_keluar . ',' . $record->longitude_keluar . '"
                                       target="_blank"
                                       style="color: #2563eb; text-decoration: underline; font-size: 14px;">
                                        🗺️ Buka di Google Maps
                                    </a>
                                </div>';
                        }

                        $content .= '
                            </div>
                        </div>

                        <div style="margin-top: 20px;">
                            <h4 style="margin: 0 0 12px 0; font-weight: 600; color: #1e293b;">🗺️ Peta Lokasi Absensi</h4>
                        </div>';

                        // Add iframe maps
                        if ($record->latitude_masuk && $record->longitude_masuk) {
                            $content .= '
                            <div style="margin-bottom: 20px;">
                                <h5 style="margin: 0 0 8px 0; color: #059669;">🟢 Lokasi Masuk</h5>
                                <div style="position: relative; width: 100%; height: 300px; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb;">
                                    <iframe
                                        src="https://www.openstreetmap.org/export/embed.html?bbox=' . ($record->longitude_masuk - 0.002) . '%2C' . ($record->latitude_masuk - 0.002) . '%2C' . ($record->longitude_masuk + 0.002) . '%2C' . ($record->latitude_masuk + 0.002) . '&amp;layer=mapnik&amp;marker=' . $record->latitude_masuk . '%2C' . $record->longitude_masuk . '"
                                        style="border: 0; width: 100%; height: 100%;"
                                        allowfullscreen>
                                    </iframe>
                                    <div style="position: absolute; bottom: 8px; left: 8px; background: rgba(255,255,255,0.9); padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                        📍 ' . $record->latitude_masuk . ', ' . $record->longitude_masuk . '
                                    </div>
                                </div>
                                <div style="margin-top: 8px; text-align: center;">
                                    <a href="https://www.google.com/maps?q=' . $record->latitude_masuk . ',' . $record->longitude_masuk . '"
                                       target="_blank"
                                       style="display: inline-block; padding: 8px 16px; background: #059669; color: white; text-decoration: none; border-radius: 6px; font-size: 14px;">
                                        🗺️ Buka di Google Maps
                                    </a>
                                </div>
                            </div>';
                        }

                        if ($record->latitude_keluar && $record->longitude_keluar) {
                            $content .= '
                            <div style="margin-bottom: 20px;">
                                <h5 style="margin: 0 0 8px 0; color: #dc2626;">🔴 Lokasi Keluar</h5>
                                <div style="position: relative; width: 100%; height: 300px; border-radius: 8px; overflow: hidden; border: 1px solid #e5e7eb;">
                                    <iframe
                                        src="https://www.openstreetmap.org/export/embed.html?bbox=' . ($record->longitude_keluar - 0.002) . '%2C' . ($record->latitude_keluar - 0.002) . '%2C' . ($record->longitude_keluar + 0.002) . '%2C' . ($record->latitude_keluar + 0.002) . '&amp;layer=mapnik&amp;marker=' . $record->latitude_keluar . '%2C' . $record->longitude_keluar . '"
                                        style="border: 0; width: 100%; height: 100%;"
                                        allowfullscreen>
                                    </iframe>
                                    <div style="position: absolute; bottom: 8px; left: 8px; background: rgba(255,255,255,0.9); padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                        📍 ' . $record->latitude_keluar . ', ' . $record->longitude_keluar . '
                                    </div>
                                </div>
                                <div style="margin-top: 8px; text-align: center;">
                                    <a href="https://www.google.com/maps?q=' . $record->latitude_keluar . ',' . $record->longitude_keluar . '"
                                       target="_blank"
                                       style="display: inline-block; padding: 8px 16px; background: #dc2626; color: white; text-decoration: none; border-radius: 6px; font-size: 14px;">
                                        🗺️ Buka di Google Maps
                                    </a>
                                </div>
                            </div>';
                        }

                        return new \Illuminate\Support\HtmlString($content);
                    })
                    ->modalWidth('6xl'),
                Tables\Actions\Action::make('approve')
                    ->label('Setujui')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->visible(fn(Absensi $record) => (Auth::user()->role === 'supervisor' || Auth::user()->role === 'admin') && is_null($record->approved_at))
                    ->action(function (Absensi $record) {
                        $record->update([
                            'approved_by' => Auth::id(),
                            'approved_at' => now(),
                        ]);

                        Notification::make()
                            ->title('Absensi berhasil disetujui')
                            ->body('Absensi karyawan ' . $record->karyawan->nama_lengkap . ' telah disetujui.')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('approve_selected')
                        ->label('Setujui Terpilih')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->visible(fn() => Auth::user()->role === 'supervisor' || Auth::user()->role === 'admin')
                        ->action(function (Collection $records) {
                            $approvedCount = 0;
                            $records->each(function ($record) use (&$approvedCount) {
                                if (is_null($record->approved_at)) {
                                    $record->update([
                                        'approved_by' => Auth::id(),
                                        'approved_at' => now(),
                                    ]);
                                    $approvedCount++;
                                }
                            });

                            Notification::make()
                                ->title('Absensi berhasil disetujui')
                                ->body($approvedCount . ' absensi telah berhasil disetujui.')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->headerActions([
                ...self::getExportActions(AbsensiExport::class, 'Data Absensi'),

                Tables\Actions\Action::make('photo_modal_script')
                    ->label('')
                    ->action(fn() => null)
                    ->modalContent(new \Illuminate\Support\HtmlString(''))
                    ->visible(false)
                    ->extraAttributes(['style' => 'display: none;'])
                    ->after(function () {
                        return new \Illuminate\Support\HtmlString('
                            <script>
                            function showPhotoModal(photoUrl, metadata, type) {
                                // Create modal
                                const modal = document.createElement("div");
                                modal.style.cssText = `
                                    position: fixed;
                                    top: 0;
                                    left: 0;
                                    width: 100%;
                                    height: 100%;
                                    background: rgba(0,0,0,0.8);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    z-index: 9999;
                                    padding: 20px;
                                `;

                                modal.innerHTML = `
                                    <div style="
                                        background: white;
                                        border-radius: 12px;
                                        max-width: 500px;
                                        max-height: 90vh;
                                        overflow: auto;
                                        position: relative;
                                    ">
                                        <div style="position: relative;">
                                            <img src="${photoUrl}" style="
                                                width: 100%;
                                                height: auto;
                                                border-radius: 12px 12px 0 0;
                                                display: block;
                                            ">

                                            <!-- Close button -->
                                            <button onclick="this.closest(\'.modal-overlay\').remove()" style="
                                                position: absolute;
                                                top: 12px;
                                                right: 12px;
                                                width: 32px;
                                                height: 32px;
                                                background: rgba(0,0,0,0.6);
                                                color: white;
                                                border: none;
                                                border-radius: 50%;
                                                cursor: pointer;
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                font-size: 18px;
                                            ">×</button>

                                            <!-- Metadata overlay -->
                                            <div style="
                                                position: absolute;
                                                bottom: 0;
                                                left: 0;
                                                right: 0;
                                                background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
                                                color: white;
                                                padding: 20px;
                                                border-radius: 0 0 12px 12px;
                                            ">
                                                <h3 style="margin: 0 0 12px 0; font-size: 18px; font-weight: 600;">
                                                    📸 Foto ${type.charAt(0).toUpperCase() + type.slice(1)}
                                                </h3>

                                                ${metadata.coordinates ? `
                                                    <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                                        <span style="font-size: 16px;">📍</span>
                                                        <span style="font-weight: 500;">${metadata.coordinates}</span>
                                                    </div>
                                                ` : ""}

                                                ${metadata.datetime ? `
                                                    <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                                        <span style="font-size: 16px;">🕐</span>
                                                        <span>${metadata.datetime}</span>
                                                    </div>
                                                ` : ""}

                                                ${metadata.status_kehadiran ? `
                                                    <div style="margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
                                                        <span style="font-size: 16px;">
                                                            ${metadata.status_kehadiran === "Tepat Waktu" ? "✅" :
                                                              metadata.status_kehadiran === "Telat" ? "⏰" : "ℹ️"}
                                                        </span>
                                                        <span style="
                                                            font-weight: 600;
                                                            color: ${metadata.status_kehadiran === "Tepat Waktu" ? "#10B981" :
                                                                     metadata.status_kehadiran === "Telat" ? "#F59E0B" : "#6B7280"};
                                                            background: rgba(255,255,255,0.2);
                                                            padding: 4px 8px;
                                                            border-radius: 4px;
                                                        ">${metadata.status_kehadiran}</span>
                                                    </div>
                                                ` : ""}

                                                ${metadata.camera ? `
                                                    <div style="margin-top: 12px; font-size: 12px; opacity: 0.8;">
                                                        📱 ${metadata.camera}
                                                    </div>
                                                ` : ""}
                                            </div>
                                        </div>
                                    </div>
                                `;

                                modal.className = "modal-overlay";
                                modal.onclick = (e) => {
                                    if (e.target === modal) {
                                        modal.remove();
                                    }
                                };

                                document.body.appendChild(modal);
                            }
                            </script>
                        ');
                    })
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAbsensis::route('/'),
            'create' => Pages\CreateAbsensi::route('/create'),
            'view' => Pages\ViewAbsensi::route('/{record}'),
            'edit' => Pages\EditAbsensi::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->with([
                'karyawan:id,nama_lengkap,nip',
                'jadwal.shift', // Load all shift fields including split shift fields
                'approvedBy:id,name'
            ]);

        // If user is supervisor, only show their supervised employees' attendance
        $user = Auth::user();
        if ($user && $user->role === 'supervisor') {
            $supervisedEmployeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
            $query->whereIn('karyawan_id', $supervisedEmployeeIds);
        }

        return $query;
    }
}
