<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Struktur Organisasi - <?php echo e($entitas->nama); ?>

                </h1>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Visualisasi hierarki organisasi dalam bentuk canvas interaktif
                </p>
            </div>
        </div>

        <!-- Controls -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="flex flex-wrap items-center gap-4">
                <!-- Zoom Controls -->
                <div class="flex items-center gap-2">
                    <button id="zoomOutBtn"
                        class="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                        </svg>
                    </button>
                    <button id="zoomInBtn"
                        class="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4">
                            </path>
                        </svg>
                    </button>
                    <button id="resetBtn"
                        class="px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors">
                        Reset
                    </button>
                    <button id="fitBtn"
                        class="px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                        Fit Screen
                    </button>
                </div>

                <!-- Zoom Info -->
                <div id="zoomInfo" class="text-sm text-gray-600 dark:text-gray-400 font-mono">
                    Zoom: 100% | Pan: 0, 0
                </div>
            </div>
        </div>

        <!-- Canvas Container -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
            <div id="canvasContainer" class="relative w-full h-[600px] overflow-hidden cursor-grab">
                <!-- Canvas Content -->
                <div class="canvas-content" id="canvasContent">
                    <!-- All content will be rendered by JavaScript -->

                    <?php
                        // Get all departments that have employees in this entity
                        $departemen = \App\Models\Departemen::whereHas('karyawan', function ($query) use ($entitas) {
                            $query->where('id_entitas', $entitas->id)->where('status_aktif', true);
                        })
                            ->with([
                                'karyawan' => function ($query) use ($entitas) {
                                    $query
                                        ->where('id_entitas', $entitas->id)
                                        ->where('status_aktif', true)
                                        ->with(['divisi', 'jabatan']);
                                },
                            ])
                            ->get();
                    ?>

                    <!-- Hidden data for JavaScript -->
                    <script type="application/json" id="orgData">
                        {
                            "entitas": {
                                "id": <?php echo e($entitas->id); ?>,
                                "nama": "<?php echo e($entitas->nama); ?>",
                                "totalKaryawan": <?php echo e($entitas->karyawan()->where('status_aktif', true)->count()); ?>

                            },
                            "departemen": [
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $departemen; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $dept): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                {
                                    "id": <?php echo e($dept->id); ?>,
                                    "nama": "<?php echo e($dept->nama_departemen); ?>",
                                    "totalKaryawan": <?php echo e($dept->karyawan->count()); ?>,
                                    "divisi": [
                                        <?php
                                            $divisiGroups = $dept->karyawan->groupBy('id_divisi');
                                        ?>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $divisiGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $divisiId => $karyawanInDivisi): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $divisi = $karyawanInDivisi->first()->divisi;
                                            ?>
                                            {
                                                "id": <?php echo e($divisiId); ?>,
                                                "nama": "<?php echo e($divisi ? $divisi->nama_divisi : 'Divisi Tidak Ditentukan'); ?>",
                                                "totalKaryawan": <?php echo e($karyawanInDivisi->count()); ?>,
                                                "karyawan": [
                                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $karyawanInDivisi; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $karyawan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    {
                                                        "id": <?php echo e($karyawan->id); ?>,
                                                        "nama": "<?php echo e($karyawan->nama_lengkap); ?>",
                                                        "jabatan": "<?php echo e($karyawan->jabatan->nama_jabatan ?? 'Jabatan Tidak Ditentukan'); ?>"
                                                    }<!--[if BLOCK]><![endif]--><?php if(!$loop->last): ?>,<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                ]
                                            }<!--[if BLOCK]><![endif]--><?php if(!$loop->last): ?>,<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    ]
                                }<!--[if BLOCK]><![endif]--><?php if(!$loop->last): ?>,<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            ]
                        }
                    </script>
                </div>

                <!-- Loading Indicator -->
                <div id="loadingIndicator"
                    class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                        <p class="mt-2 text-gray-600">Memuat struktur organisasi...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize canvas functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Get organization data
            const orgData = JSON.parse(document.getElementById('orgData').textContent);

            // Render all content
            renderOrganizationChart(orgData);

            // Initialize pan and zoom
            initializePanZoom();
        });

        // Render organization chart with JavaScript
        function renderOrganizationChart(data) {
            const content = document.getElementById('canvasContent');
            content.innerHTML = ''; // Clear existing content

            // Configuration
            const config = {
                canvasWidth: 2000,
                nodeWidth: 200,
                nodeHeight: 80,
                entitasY: 50,
                deptY: 250,
                divisiY: 500,
                karyawanStartY: 720,
                deptSpacing: 600,
                divisiSpacing: 280,
                karyawanSpacing: 150,
                lineColor: '#6b7280'
            };

            // Calculate positions
            const positions = calculatePositions(data, config);

            // Render nodes
            renderNodes(data, positions, config);

            // Render connection lines
            renderConnectionLines(data, positions, config);
        }

        function calculatePositions(data, config) {
            const positions = {
                entitas: {},
                departemen: [],
                divisi: [],
                karyawan: []
            };

            // Entitas position (centered)
            positions.entitas = {
                x: config.canvasWidth / 2 - config.nodeWidth / 2,
                y: config.entitasY,
                centerX: config.canvasWidth / 2,
                centerY: config.entitasY + config.nodeHeight / 2
            };

            // Department positions
            const deptCount = data.departemen.length;
            const totalDeptWidth = (deptCount - 1) * config.deptSpacing;
            const deptStartX = positions.entitas.centerX - totalDeptWidth / 2;

            data.departemen.forEach((dept, deptIndex) => {
                const x = deptStartX + deptIndex * config.deptSpacing - config.nodeWidth / 2;
                positions.departemen.push({
                    x: x,
                    y: config.deptY,
                    centerX: x + config.nodeWidth / 2,
                    centerY: config.deptY + config.nodeHeight / 2,
                    deptIndex: deptIndex
                });

                // Division positions for this department
                const divCount = dept.divisi.length;
                const totalDivWidth = (divCount - 1) * config.divisiSpacing;
                const divStartX = x + config.nodeWidth / 2 - totalDivWidth / 2;

                dept.divisi.forEach((div, divIndex) => {
                    const divX = divStartX + divIndex * config.divisiSpacing - config.nodeWidth / 2;
                    const divPos = {
                        x: divX,
                        y: config.divisiY,
                        centerX: divX + config.nodeWidth / 2,
                        centerY: config.divisiY + config.nodeHeight / 2,
                        deptIndex: deptIndex,
                        divIndex: divIndex
                    };
                    positions.divisi.push(divPos);

                    // Karyawan positions for this division
                    div.karyawan.forEach((kar, karIndex) => {
                        positions.karyawan.push({
                            x: divX,
                            y: config.karyawanStartY + karIndex * config.karyawanSpacing,
                            centerX: divX + config.nodeWidth / 2,
                            centerY: config.karyawanStartY + karIndex * config
                                .karyawanSpacing + config.nodeHeight / 2,
                            deptIndex: deptIndex,
                            divIndex: divIndex,
                            karIndex: karIndex
                        });
                    });
                });
            });

            return positions;
        }

        function renderNodes(data, positions, config) {
            const content = document.getElementById('canvasContent');

            // Render Entitas
            const entitasNode = createNode('entitas', data.entitas.id, {
                icon: '🏢',
                title: data.entitas.nama,
                details: `${data.entitas.totalKaryawan} Total Karyawan`
            }, positions.entitas);
            content.appendChild(entitasNode);

            // Render Departments
            data.departemen.forEach((dept, deptIndex) => {
                const deptNode = createNode('departemen', dept.id, {
                    icon: '📁',
                    title: dept.nama,
                    details: `${dept.totalKaryawan} Karyawan`,
                    extra: `${dept.divisi.length} Divisi`
                }, positions.departemen[deptIndex]);
                content.appendChild(deptNode);

                // Render Divisions
                dept.divisi.forEach((div, divIndex) => {
                    const divPos = positions.divisi.find(p => p.deptIndex === deptIndex && p.divIndex ===
                        divIndex);
                    const divNode = createNode('divisi', div.id, {
                        icon: '📂',
                        title: div.nama,
                        details: `${div.totalKaryawan} Karyawan`,
                        extra: `${div.karyawan.length} Jabatan`
                    }, divPos);
                    content.appendChild(divNode);

                    // Render Karyawan
                    div.karyawan.forEach((kar, karIndex) => {
                        const karPos = positions.karyawan.find(p =>
                            p.deptIndex === deptIndex && p.divIndex === divIndex && p
                            .karIndex === karIndex
                        );
                        const karNode = createNode('karyawan', kar.id, {
                            icon: '👤',
                            title: kar.nama,
                            details: kar.jabatan
                        }, karPos);
                        content.appendChild(karNode);
                    });
                });
            });
        }

        function createNode(type, id, content, position) {
            const node = document.createElement('div');
            node.className = `org-node ${type}`;
            node.dataset.type = type;
            node.dataset.id = id;
            node.style.cssText = `
                position: absolute;
                top: ${position.y}px;
                left: ${position.x}px;
                width: 200px;
                height: 80px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                padding: 12px;
                color: white;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                z-index: 10;
            `;

            node.innerHTML = `
                <div style="font-size: 18px; margin-bottom: 4px;">${content.icon}</div>
                <div class="font-bold" style="font-weight: bold; font-size: 14px; margin-bottom: 2px;">${content.title}</div>
                <div class="node-details" style="font-size: 11px; opacity: 0.9;">${content.details}</div>
                ${content.extra ? `<div style="font-size: 10px; opacity: 0.8;">${content.extra}</div>` : ''}
            `;

            // Add hover effect
            node.addEventListener('mouseenter', () => {
                node.style.transform = 'scale(1.05)';
                node.style.zIndex = '20';
            });

            node.addEventListener('mouseleave', () => {
                node.style.transform = 'scale(1)';
                node.style.zIndex = '10';
            });

            // Add click handler for karyawan
            if (type === 'karyawan') {
                node.addEventListener('click', () => {
                    const message =
                        `KARYAWAN: ${content.title}\n\n${content.details}\n\nKlik OK untuk melihat detail lebih lanjut`;
                    if (confirm(message)) {
                        window.location.href = `/admin/karyawans/${id}`;
                    }
                });
            }

            return node;
        }

        function renderConnectionLines(data, positions, config) {
            const content = document.getElementById('canvasContent');

            // Entitas to Departments
            if (positions.departemen.length === 1) {
                // Single department - direct line
                const dept = positions.departemen[0];
                createLine(
                    positions.entitas.centerX, positions.entitas.y + config.nodeHeight,
                    dept.centerX, dept.y,
                    config.lineColor
                );
            } else if (positions.departemen.length > 1) {
                // Multiple departments - T-junction
                const midY = positions.entitas.y + config.nodeHeight + 60;

                // Main vertical line
                createLine(
                    positions.entitas.centerX, positions.entitas.y + config.nodeHeight,
                    positions.entitas.centerX, midY,
                    config.lineColor
                );

                // Horizontal line
                const firstDept = positions.departemen[0];
                const lastDept = positions.departemen[positions.departemen.length - 1];
                createLine(
                    firstDept.centerX, midY,
                    lastDept.centerX, midY,
                    config.lineColor
                );

                // Drop lines to departments
                positions.departemen.forEach(dept => {
                    createLine(
                        dept.centerX, midY,
                        dept.centerX, dept.y,
                        config.lineColor
                    );
                });
            }

            // Departments to Divisions
            data.departemen.forEach((dept, deptIndex) => {
                const deptPos = positions.departemen[deptIndex];
                const deptDivisions = positions.divisi.filter(d => d.deptIndex === deptIndex);

                if (deptDivisions.length === 1) {
                    // Single division - direct line
                    const div = deptDivisions[0];
                    createLine(
                        deptPos.centerX, deptPos.y + config.nodeHeight,
                        div.centerX, div.y,
                        config.lineColor
                    );
                } else if (deptDivisions.length > 1) {
                    // Multiple divisions - T-junction
                    const midY = deptPos.y + config.nodeHeight + 85;

                    // Main vertical line
                    createLine(
                        deptPos.centerX, deptPos.y + config.nodeHeight,
                        deptPos.centerX, midY,
                        config.lineColor
                    );

                    // Horizontal line
                    const firstDiv = deptDivisions[0];
                    const lastDiv = deptDivisions[deptDivisions.length - 1];
                    createLine(
                        firstDiv.centerX, midY,
                        lastDiv.centerX, midY,
                        config.lineColor
                    );

                    // Drop lines to divisions
                    deptDivisions.forEach(div => {
                        createLine(
                            div.centerX, midY,
                            div.centerX, div.y,
                            config.lineColor
                        );
                    });
                }
            });

            // Divisions to Karyawan
            data.departemen.forEach((dept, deptIndex) => {
                dept.divisi.forEach((div, divIndex) => {
                    const divPos = positions.divisi.find(d => d.deptIndex === deptIndex && d.divIndex ===
                        divIndex);
                    const divKaryawan = positions.karyawan.filter(k => k.deptIndex === deptIndex && k
                        .divIndex === divIndex);

                    // Direct lines to each karyawan
                    divKaryawan.forEach(kar => {
                        createLine(
                            divPos.centerX, divPos.y + config.nodeHeight,
                            kar.centerX, kar.y,
                            config.lineColor
                        );
                    });
                });
            });
        }

        function createLine(x1, y1, x2, y2, color) {
            const content = document.getElementById('canvasContent');

            if (x1 === x2) {
                // Vertical line
                const line = document.createElement('div');
                line.style.cssText = `
                    position: absolute;
                    left: ${x1 - 1}px;
                    top: ${Math.min(y1, y2)}px;
                    width: 2px;
                    height: ${Math.abs(y2 - y1)}px;
                    background-color: ${color};
                    z-index: 1;
                `;
                content.appendChild(line);
            } else {
                // Horizontal line
                const line = document.createElement('div');
                line.style.cssText = `
                    position: absolute;
                    left: ${Math.min(x1, x2)}px;
                    top: ${y1 - 1}px;
                    width: ${Math.abs(x2 - x1)}px;
                    height: 2px;
                    background-color: ${color};
                    z-index: 1;
                `;
                content.appendChild(line);
            }
        }

        function initializePanZoom() {
            let scale = 1;
            let panX = 0;
            let panY = 0;
            let isDragging = false;
            let lastMouseX = 0;
            let lastMouseY = 0;

            const container = document.getElementById('canvasContainer');
            const content = document.getElementById('canvasContent');
            const zoomInfo = document.getElementById('zoomInfo');

            // Mouse events for panning
            container.addEventListener('mousedown', startDrag);
            container.addEventListener('mousemove', drag);
            container.addEventListener('mouseup', endDrag);
            container.addEventListener('mouseleave', endDrag);
            container.addEventListener('wheel', zoom);

            // Button event listeners
            document.getElementById('resetBtn').addEventListener('click', resetView);
            document.getElementById('zoomInBtn').addEventListener('click', zoomIn);
            document.getElementById('zoomOutBtn').addEventListener('click', zoomOut);
            document.getElementById('fitBtn').addEventListener('click', fitToScreen);

            function startDrag(e) {
                if (e.target.closest('.org-node')) return;
                isDragging = true;
                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
                container.style.cursor = 'grabbing';
            }

            function drag(e) {
                if (!isDragging) return;

                const deltaX = e.clientX - lastMouseX;
                const deltaY = e.clientY - lastMouseY;

                panX += deltaX;
                panY += deltaY;

                updateTransform();
                updateZoomInfo();

                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            }

            function endDrag() {
                isDragging = false;
                container.style.cursor = 'grab';
            }

            function zoom(e) {
                e.preventDefault();

                const rect = container.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                const newScale = Math.max(0.1, Math.min(3, scale * delta));

                // Adjust pan to zoom towards mouse position
                panX = mouseX - (mouseX - panX) * (newScale / scale);
                panY = mouseY - (mouseY - panY) * (newScale / scale);

                scale = newScale;
                updateTransform();
                updateZoomInfo();
            }

            function updateTransform() {
                content.style.transform = `translate(${panX}px, ${panY}px) scale(${scale})`;
            }

            function updateZoomInfo() {
                zoomInfo.textContent = `Zoom: ${Math.round(scale * 100)}% | Pan: ${Math.round(panX)}, ${Math.round(panY)}`;
            }

            function resetView() {
                scale = 1;
                panX = 0;
                panY = 0;
                updateTransform();
                updateZoomInfo();
            }

            function zoomIn() {
                scale = Math.min(3, scale * 1.2);
                updateTransform();
                updateZoomInfo();
            }

            function zoomOut() {
                scale = Math.max(0.1, scale * 0.8);
                updateTransform();
                updateZoomInfo();
            }

            function fitToScreen() {
                const containerRect = container.getBoundingClientRect();
                const scaleX = containerRect.width / 2000;
                const scaleY = containerRect.height / 1500;
                scale = Math.min(scaleX, scaleY, 1);

                panX = (containerRect.width - 2000 * scale) / 2;
                panY = (containerRect.height - 1500 * scale) / 2;

                updateTransform();
                updateZoomInfo();
            }

            // Initialize
            updateZoomInfo();

            // Hide loading indicator
            setTimeout(() => {
                const loading = document.getElementById('loadingIndicator');
                if (loading) {
                    loading.style.opacity = '0';
                    setTimeout(() => {
                        loading.style.display = 'none';
                    }, 300);
                }
            }, 1000);
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/pages/struktur-organisasi-canvas.blade.php ENDPATH**/ ?>